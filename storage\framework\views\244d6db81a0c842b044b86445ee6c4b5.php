<?php $__env->startSection('title', 'Application Tracker'); ?>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/scholarship.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <style>
        .tracker-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .tracker-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .tracker-title {
            font-size: 24px;
            color: #1e5631;
            margin-bottom: 10px;
        }

        .tracker-description {
            color: #666;
            font-size: 16px;
        }



        .result-container {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .application-details {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 25px;
        }

        .application-id {
            font-size: 20px;
            color: #1e5631;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .detail-row {
            display: flex;
            margin-bottom: 15px;
        }

        .detail-label {
            width: 150px;
            font-size: 14px;
            color: #666;
            flex-shrink: 0;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-badge.pending {
            background-color: #fff8e1;
            color: #f57f17;
        }

        .status-badge.review {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .status-badge.approved {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-badge.rejected {
            background-color: #ffebee;
            color: #c62828;
        }

        .status-timeline {
            margin-top: 30px;
        }

        .timeline-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 7px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #ddd;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 25px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -30px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #ddd;
        }

        .timeline-dot.active {
            background-color: #1e5631;
        }

        .timeline-content {
            padding-left: 10px;
        }

        .timeline-status {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .timeline-date {
            font-size: 14px;
            color: #666;
        }

        .timeline-description {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .no-result {
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .no-result i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        .no-result-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 14px;
            text-decoration: none;
            margin-top: 20px;
        }

        .back-button:hover {
            background-color: #e5e5e5;
        }

        .my-applications {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 10px;
        }

        .my-applications-title {
            font-size: 18px;
            color: #1e5631;
            margin-bottom: 15px;
        }

        .applications-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .application-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 15px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .application-item:hover {
            border-color: #1e5631;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .application-info {
            flex: 1;
        }

        .application-id-text {
            font-weight: 600;
            color: #1e5631;
            margin-bottom: 5px;
        }

        .application-meta {
            font-size: 14px;
            color: #666;
        }

        .application-status {
            margin-left: 15px;
        }

        .no-applications {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        /* Minimalistic Status Notifications for Tracker */
        .tracker-status-notification {
            background: #ffffff;
            border: 1px solid #e8f5e8;
            border-left: 4px solid #28a745;
            border-radius: 8px;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .tracker-status-notification.rejected {
            border-left-color: #dc3545;
            border-color: #fdf2f2;
        }

        .tracker-status-notification .notification-content {
            display: flex;
            align-items: center;
            padding: 20px 24px;
            gap: 16px;
        }

        .tracker-status-notification .notification-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #28a745;
        }

        .tracker-status-notification.rejected .notification-icon {
            border-color: #dc3545;
        }

        .tracker-status-notification .notification-icon i {
            color: #28a745;
            font-size: 1.2rem;
        }

        .tracker-status-notification.rejected .notification-icon i {
            color: #dc3545;
        }

        .tracker-status-notification .notification-text {
            flex: 1;
            color: #2c3e50;
        }

        .tracker-status-notification .notification-text .status-title {
            color: #28a745;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 4px;
            display: block;
        }

        .tracker-status-notification.rejected .notification-text .status-title {
            color: #dc3545;
        }

        .tracker-status-notification .notification-text .status-details {
            font-size: 0.9rem;
            margin-bottom: 8px;
            line-height: 1.4;
            color: #6c757d;
        }

        .tracker-status-notification .notification-text .status-info {
            font-size: 0.8rem;
            color: #adb5bd;
        }

        .tracker-status-notification .notification-badge {
            flex-shrink: 0;
            background: #28a745;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .tracker-status-notification.rejected .notification-badge {
            background: #dc3545;
        }

        /* Enhanced Application Details for Final Status */
        .final-status-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #28a745;
        }

        .final-status-details.rejected {
            border-left-color: #dc3545;
        }

        .final-status-details .status-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .final-status-details .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #28a745;
            color: white;
        }

        .final-status-details.rejected .status-icon {
            background: #dc3545;
        }

        .final-status-details .status-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #28a745;
            margin: 0;
        }

        .final-status-details.rejected .status-title {
            color: #dc3545;
        }

        .final-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .final-status-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .final-status-item .label {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .final-status-item .value {
            font-size: 0.95rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .final-status-item.highlight .value {
            color: #28a745;
            font-weight: 700;
        }

        .final-status-item.highlight.rejected .value {
            color: #dc3545;
        }

        /* Responsive Design for Tracker Notifications */
        @media (max-width: 768px) {
            .tracker-status-notification {
                margin: 15px 10px;
                max-width: calc(100% - 20px);
            }

            .tracker-status-notification .notification-content {
                padding: 16px 20px;
                gap: 12px;
            }

            .final-status-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        @media (max-width: 480px) {
            .tracker-status-notification {
                margin: 10px 5px;
            }

            .tracker-status-notification .notification-content {
                padding: 12px 16px;
                gap: 10px;
                flex-direction: column;
                text-align: center;
            }

            .final-status-details {
                padding: 16px;
            }
        }
    </style>

    <div class="page-container">
        <div class="application-container">
            <div class="tracker-container">
                <div class="tracker-header">
                    <h1 class="tracker-title">Application Status Tracker</h1>
                    <p class="tracker-description">Track your scholarship applications by clicking on them below.</p>
                </div>

                <!-- Minimalistic Status Notifications for Tracker -->
                <?php if($permanentStatus): ?>
                    <div class="tracker-status-notification <?php echo e($permanentStatus->status === 'Rejected' ? 'rejected' : ''); ?>">
                        <div class="notification-content">
                            <div class="notification-icon">
                                <?php if($permanentStatus->status === 'Approved'): ?>
                                    <i class="fas fa-check"></i>
                                <?php else: ?>
                                    <i class="fas fa-times"></i>
                                <?php endif; ?>
                            </div>
                            <div class="notification-text">
                                <span class="status-title">
                                    <?php if($permanentStatus->status === 'Approved'): ?>
                                        Scholarship Approved
                                    <?php else: ?>
                                        Application Rejected
                                    <?php endif; ?>
                                </span>
                                <div class="status-details">
                                    <?php if($permanentStatus->status === 'Approved'): ?>
                                        Your <?php echo e(ucfirst($permanentStatus->scholarship_type)); ?> scholarship application has been approved.
                                        <?php if($permanentStatus->scholarship_subtype): ?>
                                            Awarded: <?php echo e($permanentStatus->scholarship_subtype); ?> Scholarship
                                        <?php endif; ?>
                                    <?php else: ?>
                                        Your <?php echo e(ucfirst($permanentStatus->scholarship_type)); ?> scholarship application was not approved.
                                    <?php endif; ?>
                                </div>
                                <div class="status-info">
                                    <?php echo e($permanentStatus->application_id); ?> • <?php echo e($permanentStatus->updated_at->format('M d, Y')); ?>

                                    <?php if($permanentStatus->status === 'Approved'): ?>
                                        • Active Scholar
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="notification-badge">
                                <?php if($permanentStatus->status === 'Approved'): ?>
                                    APPROVED
                                <?php else: ?>
                                    REJECTED
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($userApplications && $userApplications->count() > 0): ?>
                    <div class="my-applications">
                        <h3 class="my-applications-title">My Applications</h3>
                        <div class="applications-list">
                            <?php $__currentLoopData = $userApplications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $app): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="application-item" onclick="trackApplication('<?php echo e($app->application_id); ?>')">
                                    <div class="application-info">
                                        <div class="application-id-text"><?php echo e($app->application_id); ?></div>
                                        <div class="application-meta">
                                            <?php if($app->scholarship_type == 'government'): ?>
                                                Government Scholarship
                                            <?php elseif($app->scholarship_type == 'academic'): ?>
                                                President's Scholarship
                                            <?php elseif($app->scholarship_type == 'employees'): ?>
                                                Employees Scholar
                                            <?php elseif($app->scholarship_type == 'private'): ?>
                                                Private Scholarship
                                            <?php else: ?>
                                                <?php echo e(ucfirst($app->scholarship_type)); ?> Scholarship
                                            <?php endif; ?>
                                            • Applied <?php echo e($app->created_at->format('M d, Y')); ?>

                                        </div>
                                    </div>
                                    <div class="application-status">
                                        <span
                                            class="status-badge
                                        <?php if($app->status == 'Pending Review'): ?> pending
                                        <?php elseif($app->status == 'Under Committee Review'): ?> review
                                        <?php elseif($app->status == 'Approved'): ?> approved
                                        <?php elseif($app->status == 'Rejected'): ?> rejected <?php endif; ?>">
                                            <?php echo e($app->status); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="my-applications">
                        <h3 class="my-applications-title">My Applications</h3>
                        <div class="no-applications">
                            You haven't submitted any scholarship applications yet.
                        </div>
                    </div>
                <?php endif; ?>



                <?php
                    // Use the application passed from the controller
                    // $application is already set by the controller
                ?>

                <?php if(request('id') && $application): ?>
                    <div class="result-container">

                        <div class="application-details">
                            <div class="application-id">Application ID: <?php echo e($application->application_id); ?></div>

                            <div class="detail-row">
                                <div class="detail-label">Student Name</div>
                                <div class="detail-value"><?php echo e($application->first_name); ?> <?php echo e($application->last_name); ?>

                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Student ID</div>
                                <div class="detail-value"><?php echo e($application->student_id); ?></div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Scholarship Type</div>
                                <div class="detail-value">
                                    <?php if($application->scholarship_type == 'government'): ?>
                                        Government Scholarship
                                    <?php elseif($application->scholarship_type == 'academic'): ?>
                                        President's Scholarship
                                    <?php elseif($application->scholarship_type == 'employees'): ?>
                                        Employees Scholar
                                    <?php elseif($application->scholarship_type == 'private'): ?>
                                        Private Scholarship
                                    <?php else: ?>
                                        <?php echo e(ucfirst($application->scholarship_type)); ?> Scholarship
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Date Applied</div>
                                <div class="detail-value"><?php echo e($application->created_at->format('F d, Y')); ?></div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Current Status</div>
                                <div class="detail-value">
                                    <span
                                        class="status-badge
                                    <?php if($application->status == 'Pending Review'): ?> pending
                                    <?php elseif($application->status == 'Under Committee Review'): ?> review
                                    <?php elseif($application->status == 'Approved'): ?> approved
                                    <?php elseif($application->status == 'Rejected'): ?> rejected <?php endif; ?>">
                                        <?php echo e($application->status); ?>

                                    </span>
                                </div>
                            </div>

                            <?php if($application->status === 'Approved'): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Decision Date</div>
                                    <div class="detail-value"><?php echo e($application->updated_at->format('F d, Y')); ?></div>
                                </div>
                                <?php if($application->scholarship_subtype): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Scholarship Award</div>
                                        <div class="detail-value"><?php echo e($application->scholarship_subtype); ?> Scholarship</div>
                                    </div>
                                <?php endif; ?>
                                <?php if($application->gwa): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">GWA</div>
                                        <div class="detail-value"><?php echo e($application->gwa); ?></div>
                                    </div>
                                <?php endif; ?>
                            <?php elseif($application->status === 'Rejected'): ?>
                                <div class="detail-row">
                                    <div class="detail-label">Decision Date</div>
                                    <div class="detail-value"><?php echo e($application->updated_at->format('F d, Y')); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if(in_array($application->status, ['Approved', 'Rejected'])): ?>
                            <div class="final-status-details <?php echo e($application->status === 'Rejected' ? 'rejected' : ''); ?>">
                                <div class="status-header">
                                    <div class="status-icon">
                                        <?php if($application->status === 'Approved'): ?>
                                            <i class="fas fa-trophy"></i>
                                        <?php else: ?>
                                            <i class="fas fa-info-circle"></i>
                                        <?php endif; ?>
                                    </div>
                                    <h4 class="status-title">
                                        <?php if($application->status === 'Approved'): ?>
                                            Scholarship Details
                                        <?php else: ?>
                                            Application Information
                                        <?php endif; ?>
                                    </h4>
                                </div>

                                <div class="final-status-grid">
                                    <div class="final-status-item">
                                        <div class="label">Application ID</div>
                                        <div class="value"><?php echo e($application->application_id); ?></div>
                                    </div>
                                    <div class="final-status-item">
                                        <div class="label">Student ID</div>
                                        <div class="value"><?php echo e($application->student_id); ?></div>
                                    </div>
                                    <div class="final-status-item">
                                        <div class="label">Scholarship Type</div>
                                        <div class="value"><?php echo e(ucfirst($application->scholarship_type)); ?></div>
                                    </div>
                                    <?php if($application->scholarship_subtype): ?>
                                        <div class="final-status-item highlight <?php echo e($application->status === 'Rejected' ? 'rejected' : ''); ?>">
                                            <div class="label">Award Type</div>
                                            <div class="value"><?php echo e($application->scholarship_subtype); ?> Scholarship</div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($application->department): ?>
                                        <div class="final-status-item">
                                            <div class="label">Department</div>
                                            <div class="value"><?php echo e($application->department); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($application->course): ?>
                                        <div class="final-status-item">
                                            <div class="label">Course</div>
                                            <div class="value"><?php echo e($application->course); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($application->year_level): ?>
                                        <div class="final-status-item">
                                            <div class="label">Year Level</div>
                                            <div class="value"><?php echo e($application->year_level); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($application->gwa): ?>
                                        <div class="final-status-item highlight <?php echo e($application->status === 'Rejected' ? 'rejected' : ''); ?>">
                                            <div class="label">GWA</div>
                                            <div class="value"><?php echo e($application->gwa); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="final-status-item">
                                        <div class="label">Application Date</div>
                                        <div class="value"><?php echo e($application->created_at->format('F d, Y')); ?></div>
                                    </div>
                                    <div class="final-status-item highlight <?php echo e($application->status === 'Rejected' ? 'rejected' : ''); ?>">
                                        <div class="label">Decision Date</div>
                                        <div class="value"><?php echo e($application->updated_at->format('F d, Y')); ?></div>
                                    </div>
                                    <?php if($application->status === 'Approved'): ?>
                                        <div class="final-status-item highlight">
                                            <div class="label">Scholar Status</div>
                                            <div class="value">Active Scholar</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="status-timeline">
                            <h3 class="timeline-title">Application Timeline</h3>

                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-dot active"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Application Submitted</div>
                                        <div class="timeline-date"><?php echo e($application->created_at->format('F d, Y')); ?>

                                        </div>
                                        <div class="timeline-description">Your application has been successfully
                                            submitted
                                            and is awaiting review.</div>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div
                                        class="timeline-dot <?php echo e(in_array($application->status, ['Under Committee Review', 'Approved', 'Rejected']) ? 'active' : ''); ?>">
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Under Committee Review</div>
                                        <div class="timeline-date">
                                            <?php echo e($application->status == 'Pending Review' ? 'Pending' : $application->updated_at->format('F d, Y')); ?>

                                        </div>
                                        <div class="timeline-description">Your application is being reviewed by the
                                            scholarship committee.</div>
                                    </div>
                                </div>



                                <div class="timeline-item">
                                    <div
                                        class="timeline-dot <?php echo e(in_array($application->status, ['Approved', 'Rejected']) ? 'active' : ''); ?>">
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Final Status: <?php echo e($application->status); ?></div>
                                        <div class="timeline-date">
                                            <?php echo e(in_array($application->status, ['Pending Review', 'Under Committee Review']) ? 'Pending' : $application->updated_at->format('F d, Y')); ?>

                                        </div>
                                        <div class="timeline-description">
                                            <?php if($application->status == 'Approved'): ?>
                                                Congratulations! Your scholarship application has been approved. Please
                                                check your email for further instructions.
                                            <?php elseif($application->status == 'Rejected'): ?>
                                                We regret to inform you that your scholarship application has been
                                                rejected.
                                                Please contact the Office of the Registrar for more information.
                                            <?php else: ?>
                                                The final decision on your application is pending.
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <a href="<?php echo e(route('student.dashboard')); ?>" class="back-button">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                <?php elseif(request('id')): ?>
                    <div class="result-container">
                        <div class="no-result">
                            <i class="fas fa-search"></i>
                            <h3 class="no-result-title">No Application Found</h3>
                            <p>We couldn't find an application with the ID "<?php echo e(request('id')); ?>". Please check the ID
                                and
                                try again.</p>

                            <a href="<?php echo e(route('student.dashboard')); ?>" class="back-button">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function trackApplication(applicationId) {
            // Redirect directly to the tracker with the application ID
            window.location.href = "<?php echo e(route('scholarship.tracker')); ?>?id=" + applicationId;
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.student', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/scholarship/tracker.blade.php ENDPATH**/ ?>