@extends('layouts.student')

@section('title', 'Application Tracker')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/scholarship.css') }}">
@endpush

@section('content')
    <style>
        .tracker-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .tracker-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .tracker-title {
            font-size: 24px;
            color: #1e5631;
            margin-bottom: 10px;
        }

        .tracker-description {
            color: #666;
            font-size: 16px;
        }



        .result-container {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .application-details {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 25px;
        }

        .application-id {
            font-size: 20px;
            color: #1e5631;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .detail-row {
            display: flex;
            margin-bottom: 15px;
        }

        .detail-label {
            width: 150px;
            font-size: 14px;
            color: #666;
            flex-shrink: 0;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-badge.pending {
            background-color: #fff8e1;
            color: #f57f17;
        }

        .status-badge.review {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .status-badge.approved {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-badge.rejected {
            background-color: #ffebee;
            color: #c62828;
        }

        .status-timeline {
            margin-top: 30px;
        }

        .timeline-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 7px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #ddd;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 25px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -30px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #ddd;
        }

        .timeline-dot.active {
            background-color: #1e5631;
        }

        .timeline-content {
            padding-left: 10px;
        }

        .timeline-status {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .timeline-date {
            font-size: 14px;
            color: #666;
        }

        .timeline-description {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .no-result {
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .no-result i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        .no-result-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 14px;
            text-decoration: none;
            margin-top: 20px;
        }

        .back-button:hover {
            background-color: #e5e5e5;
        }

        .my-applications {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 10px;
        }

        .my-applications-title {
            font-size: 18px;
            color: #1e5631;
            margin-bottom: 15px;
        }

        .applications-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .application-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 15px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .application-item:hover {
            border-color: #1e5631;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .application-info {
            flex: 1;
        }

        .application-id-text {
            font-weight: 600;
            color: #1e5631;
            margin-bottom: 5px;
        }

        .application-meta {
            font-size: 14px;
            color: #666;
        }

        .application-status {
            margin-left: 15px;
        }

        .no-applications {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        /* Permanent Status Notifications for Tracker */
        .tracker-permanent-notification {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border: 3px solid #28a745;
            border-radius: 15px;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
            position: relative;
            overflow: hidden;
            animation: trackerNotificationPulse 3s ease-in-out infinite;
        }

        .tracker-permanent-notification.rejected {
            background: linear-gradient(135deg, #fdf2f2, #f8d7da);
            border-color: #dc3545;
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.2);
        }

        @keyframes trackerNotificationPulse {
            0%, 100% {
                box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 15px 40px rgba(40, 167, 69, 0.3);
                transform: scale(1.01);
            }
        }

        .tracker-permanent-notification.rejected {
            animation: trackerNotificationPulseRed 3s ease-in-out infinite;
        }

        @keyframes trackerNotificationPulseRed {
            0%, 100% {
                box-shadow: 0 10px 30px rgba(220, 53, 69, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 15px 40px rgba(220, 53, 69, 0.3);
                transform: scale(1.01);
            }
        }

        .tracker-permanent-notification::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997, #28a745);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        .tracker-permanent-notification.rejected::before {
            background: linear-gradient(90deg, #dc3545, #e74c3c, #dc3545);
            background-size: 200% 100%;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .tracker-permanent-notification .notification-content {
            display: flex;
            align-items: center;
            padding: 25px;
            gap: 20px;
        }

        .tracker-permanent-notification .notification-icon {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }

        .tracker-permanent-notification.rejected .notification-icon {
            background: #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .tracker-permanent-notification .notification-icon i {
            color: white;
            font-size: 1.8rem;
        }

        .tracker-permanent-notification .notification-text {
            flex: 1;
            color: #155724;
            font-size: 1rem;
            line-height: 1.6;
        }

        .tracker-permanent-notification.rejected .notification-text {
            color: #721c24;
        }

        .tracker-permanent-notification .notification-text .status-title {
            color: #28a745;
            font-weight: 800;
            font-size: 1.3rem;
            margin-bottom: 8px;
            display: block;
        }

        .tracker-permanent-notification.rejected .notification-text .status-title {
            color: #dc3545;
        }

        .tracker-permanent-notification .notification-text .status-details {
            font-size: 1rem;
            margin-bottom: 12px;
            line-height: 1.5;
            font-weight: 500;
        }

        .tracker-permanent-notification .notification-text .status-info {
            font-size: 0.9rem;
            opacity: 0.9;
            font-style: italic;
        }

        .tracker-permanent-notification .notification-badge {
            flex-shrink: 0;
            background: #28a745;
            color: white;
            padding: 10px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .tracker-permanent-notification.rejected .notification-badge {
            background: #dc3545;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        /* Responsive Design for Tracker Notifications */
        @media (max-width: 768px) {
            .tracker-permanent-notification {
                margin: 15px 10px;
                max-width: calc(100% - 20px);
            }

            .tracker-permanent-notification .notification-content {
                padding: 20px;
                gap: 15px;
                flex-direction: column;
                text-align: center;
            }

            .tracker-permanent-notification .notification-icon {
                width: 50px;
                height: 50px;
            }

            .tracker-permanent-notification .notification-icon i {
                font-size: 1.5rem;
            }

            .tracker-permanent-notification .notification-text .status-title {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .tracker-permanent-notification {
                margin: 10px 5px;
            }

            .tracker-permanent-notification .notification-content {
                padding: 15px;
                gap: 12px;
            }

            .tracker-permanent-notification .notification-text .status-details {
                font-size: 0.9rem;
            }

            .tracker-permanent-notification .notification-text .status-info {
                font-size: 0.8rem;
            }

            .tracker-permanent-notification .notification-badge {
                font-size: 0.8rem;
                padding: 8px 12px;
            }
        }
    </style>

    <div class="page-container">
        <div class="application-container">
            <div class="tracker-container">
                <div class="tracker-header">
                    <h1 class="tracker-title">Application Status Tracker</h1>
                    <p class="tracker-description">Track your scholarship applications by clicking on them below.</p>
                </div>

                <!-- Permanent Status Notifications for Tracker -->
                @if ($permanentStatus)
                    @if ($permanentStatus->status === 'Approved')
                        <div class="tracker-permanent-notification" id="tracker-permanent-notification">
                            <div class="notification-content">
                                <div class="notification-icon">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="notification-text">
                                    <span class="status-title">🎉 Scholarship Approved!</span>
                                    <div class="status-details">
                                        You have been approved for the {{ ucfirst($permanentStatus->scholarship_type) }} Scholarship.
                                    </div>
                                    <div class="status-info">
                                        Application ID: {{ $permanentStatus->application_id }} •
                                        Approved: {{ $permanentStatus->updated_at->format('F d, Y') }}
                                    </div>
                                </div>
                                <div class="notification-badge">
                                    Scholar
                                </div>
                            </div>
                        </div>
                    @elseif ($permanentStatus->status === 'Rejected')
                        <div class="tracker-permanent-notification rejected" id="tracker-permanent-notification">
                            <div class="notification-content">
                                <div class="notification-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="notification-text">
                                    <span class="status-title">Application Status Update</span>
                                    <div class="status-details">
                                        Your {{ ucfirst($permanentStatus->scholarship_type) }} Scholarship application was not approved.
                                    </div>
                                    <div class="status-info">
                                        Application ID: {{ $permanentStatus->application_id }} •
                                        Decision: {{ $permanentStatus->updated_at->format('F d, Y') }}
                                    </div>
                                </div>
                                <div class="notification-badge">
                                    Not Approved
                                </div>
                            </div>
                        </div>
                    @endif
                @endif

                @if ($userApplications && $userApplications->count() > 0)
                    <div class="my-applications">
                        <h3 class="my-applications-title">My Applications</h3>
                        <div class="applications-list">
                            @foreach ($userApplications as $app)
                                <div class="application-item" onclick="trackApplication('{{ $app->application_id }}')">
                                    <div class="application-info">
                                        <div class="application-id-text">{{ $app->application_id }}</div>
                                        <div class="application-meta">
                                            @if ($app->scholarship_type == 'government')
                                                Government Scholarship
                                            @elseif($app->scholarship_type == 'academic')
                                                President's Scholarship
                                            @elseif($app->scholarship_type == 'employees')
                                                Employees Scholar
                                            @elseif($app->scholarship_type == 'private')
                                                Private Scholarship
                                            @else
                                                {{ ucfirst($app->scholarship_type) }} Scholarship
                                            @endif
                                            • Applied {{ $app->created_at->format('M d, Y') }}
                                        </div>
                                    </div>
                                    <div class="application-status">
                                        <span
                                            class="status-badge
                                        @if ($app->status == 'Pending Review') pending
                                        @elseif($app->status == 'Under Committee Review') review
                                        @elseif($app->status == 'Approved') approved
                                        @elseif($app->status == 'Rejected') rejected @endif">
                                            {{ $app->status }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @else
                    <div class="my-applications">
                        <h3 class="my-applications-title">My Applications</h3>
                        <div class="no-applications">
                            You haven't submitted any scholarship applications yet.
                        </div>
                    </div>
                @endif



                @php
                    // Use the application passed from the controller
                    // $application is already set by the controller
                @endphp

                @if (request('id') && $application)
                    <div class="result-container">

                        <div class="application-details">
                            <div class="application-id">Application ID: {{ $application->application_id }}</div>

                            <div class="detail-row">
                                <div class="detail-label">Student Name</div>
                                <div class="detail-value">{{ $application->first_name }} {{ $application->last_name }}
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Student ID</div>
                                <div class="detail-value">{{ $application->student_id }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Scholarship Type</div>
                                <div class="detail-value">
                                    @if ($application->scholarship_type == 'government')
                                        Government Scholarship
                                    @elseif($application->scholarship_type == 'academic')
                                        President's Scholarship
                                    @elseif($application->scholarship_type == 'employees')
                                        Employees Scholar
                                    @elseif($application->scholarship_type == 'private')
                                        Private Scholarship
                                    @else
                                        {{ ucfirst($application->scholarship_type) }} Scholarship
                                    @endif
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Date Applied</div>
                                <div class="detail-value">{{ $application->created_at->format('F d, Y') }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">Current Status</div>
                                <div class="detail-value">
                                    <span
                                        class="status-badge
                                    @if ($application->status == 'Pending Review') pending
                                    @elseif($application->status == 'Under Committee Review') review
                                    @elseif($application->status == 'Approved') approved
                                    @elseif($application->status == 'Rejected') rejected @endif">
                                        {{ $application->status }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="status-timeline">
                            <h3 class="timeline-title">Application Timeline</h3>

                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-dot active"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Application Submitted</div>
                                        <div class="timeline-date">{{ $application->created_at->format('F d, Y') }}
                                        </div>
                                        <div class="timeline-description">Your application has been successfully
                                            submitted
                                            and is awaiting review.</div>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div
                                        class="timeline-dot {{ in_array($application->status, ['Under Committee Review', 'Approved', 'Rejected']) ? 'active' : '' }}">
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Under Committee Review</div>
                                        <div class="timeline-date">
                                            {{ $application->status == 'Pending Review' ? 'Pending' : $application->updated_at->format('F d, Y') }}
                                        </div>
                                        <div class="timeline-description">Your application is being reviewed by the
                                            scholarship committee.</div>
                                    </div>
                                </div>



                                <div class="timeline-item">
                                    <div
                                        class="timeline-dot {{ in_array($application->status, ['Approved', 'Rejected']) ? 'active' : '' }}">
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-status">Final Status: {{ $application->status }}</div>
                                        <div class="timeline-date">
                                            {{ in_array($application->status, ['Pending Review', 'Under Committee Review']) ? 'Pending' : $application->updated_at->format('F d, Y') }}
                                        </div>
                                        <div class="timeline-description">
                                            @if ($application->status == 'Approved')
                                                Congratulations! Your scholarship application has been approved. Please
                                                check your email for further instructions.
                                            @elseif($application->status == 'Rejected')
                                                We regret to inform you that your scholarship application has been
                                                rejected.
                                                Please contact the Office of the Registrar for more information.
                                            @else
                                                The final decision on your application is pending.
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <a href="{{ route('student.dashboard') }}" class="back-button">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                @elseif(request('id'))
                    <div class="result-container">
                        <div class="no-result">
                            <i class="fas fa-search"></i>
                            <h3 class="no-result-title">No Application Found</h3>
                            <p>We couldn't find an application with the ID "{{ request('id') }}". Please check the ID
                                and
                                try again.</p>

                            <a href="{{ route('student.dashboard') }}" class="back-button">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script>
        function trackApplication(applicationId) {
            // Redirect directly to the tracker with the application ID
            window.location.href = "{{ route('scholarship.tracker') }}?id=" + applicationId;
        }
    </script>
@endpush
